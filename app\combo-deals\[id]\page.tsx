"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, ShoppingCart, Heart, Share2, Clock, Package } from "lucide-react";
import NavBar from "@/components/navbar";
import NavBarSkeleton from "@/components/navBarSkeleton";
import Footer from "@/components/footer";
import { useSession } from "@/lib/auth-client";
import { User } from "@/utils/types";
import { useCart } from "@/contexts/cart-context";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { toast } from "react-hot-toast";
import { motion } from "framer-motion";

type ComboDeal = {
  id: string;
  name: string;
  description?: string;
  originalPrice: number;
  comboPrice: number;
  discount: number;
  discountPercent: number;
  validUntil: string;
  isActive: boolean;
  isFeatured: boolean;
  comboImage?: string;
  comboProducts: Array<{
    id: string;
    quantity: number;
    product: {
      id: string;
      name: string;
      brand: string;
      price: number;
      discountedPrice?: number;
      images: string[];
      stock: number;
      isActive: boolean;
    };
  }>;
};

export default function ComboDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, isPending } = useSession();
  const { addToCart } = useCart();
  const [user, setUser] = useState<User | null>(null);
  const [combo, setCombo] = useState<ComboDeal | null>(null);
  const [loading, setLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchCombo();
    }
  }, [params.id]);

  const fetchCombo = async () => {
    try {
      const response = await fetch(`/api/combo-deals/${params.id}`);
      const result = await response.json();
      
      if (result.success) {
        setCombo(result.data);
      } else {
        toast.error("Combo deal not found");
        router.push("/");
      }
    } catch (error) {
      console.error("Error fetching combo:", error);
      toast.error("Failed to load combo deal");
      router.push("/");
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async () => {
    if (!combo) return;

    setAddingToCart(true);
    try {
      // Add combo to cart (this would need to be implemented in cart context)
      // For now, we'll add individual products
      for (const comboProduct of combo.comboProducts) {
        await addToCart({
          productId: comboProduct.product.id,
          quantity: comboProduct.quantity,
          size: "", // Default size
          color: "", // Default color
        });
      }
      
      toast.success("Combo deal added to cart!");
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add combo to cart");
    } finally {
      setAddingToCart(false);
    }
  };

  const isExpired = combo ? new Date(combo.validUntil) < new Date() : false;
  const isAvailable = combo ? combo.isActive && !isExpired && 
    combo.comboProducts.every(cp => cp.product.isActive && cp.product.stock > 0) : false;

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        {session?.user ? (
          <NavBar user={session.user as User} loading={isPending} />
        ) : (
          <NavBarSkeleton loading={isPending} user={null} />
        )}
        <div className="flex justify-center items-center h-96">
          <SpinnerCircle4 />
        </div>
        <Footer />
      </div>
    );
  }

  if (!combo) {
    return (
      <div className="min-h-screen bg-white">
        {session?.user ? (
          <NavBar user={session.user as User} loading={isPending} />
        ) : (
          <NavBarSkeleton loading={isPending} user={null} />
        )}
        <div className="max-w-4xl mx-auto py-8 px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Combo Deal Not Found</h1>
          <Button onClick={() => router.push("/")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {session?.user ? (
        <NavBar user={session.user as User} loading={isPending} />
      ) : (
        <NavBarSkeleton loading={isPending} user={null} />
      )}

      <div className="max-w-7xl mx-auto py-8 px-4">
        {/* Back Button */}
        <Button variant="outline" onClick={() => router.back()} className="mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4">
                  <Image
                    src={combo.comboImage || combo.comboProducts[0]?.product.images[0] || "/placeholder.png"}
                    alt={combo.name}
                    width={600}
                    height={600}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Product Grid */}
                <div className="grid grid-cols-2 gap-3">
                  {combo.comboProducts.map((cp) => (
                    <div key={cp.id} className="aspect-square bg-gray-50 rounded-lg overflow-hidden">
                      <Image
                        src={cp.product.images[0] || "/placeholder.png"}
                        alt={cp.product.name}
                        width={200}
                        height={200}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Details Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            {/* Header */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="destructive">COMBO DEAL</Badge>
                {combo.isFeatured && <Badge variant="secondary">FEATURED</Badge>}
                {!isAvailable && <Badge variant="outline">UNAVAILABLE</Badge>}
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{combo.name}</h1>
              {combo.description && (
                <p className="text-gray-600">{combo.description}</p>
              )}
            </div>

            {/* Pricing */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="text-3xl font-bold text-green-600">
                      M{combo.comboPrice.toFixed(2)}
                    </span>
                    <span className="text-xl text-gray-500 line-through">
                      M{combo.originalPrice.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive" className="text-sm">
                      {combo.discountPercent.toFixed(0)}% OFF
                    </Badge>
                    <span className="text-green-600 font-medium">
                      Save M{combo.discount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Validity */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2 text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">
                    Valid until: {new Date(combo.validUntil).toLocaleDateString()}
                    {isExpired && <span className="text-red-600 ml-2">(Expired)</span>}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Products Included */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Products Included ({combo.comboProducts.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {combo.comboProducts.map((cp) => (
                    <div key={cp.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Image
                        src={cp.product.images[0] || "/placeholder.png"}
                        alt={cp.product.name}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium">{cp.product.brand} {cp.product.name}</h4>
                        <p className="text-sm text-gray-600">
                          M{(cp.product.discountedPrice || cp.product.price).toFixed(2)}
                        </p>
                        <p className="text-xs text-gray-500">
                          Stock: {cp.product.stock} • Qty: {cp.quantity}
                        </p>
                      </div>
                      <Link href={`/products/${cp.product.id}`}>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </Link>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleAddToCart}
                disabled={!isAvailable || addingToCart}
                className="w-full"
                size="lg"
              >
                {addingToCart ? (
                  <>
                    <SpinnerCircle4 />
                    Adding to Cart...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    {isAvailable ? "Add Combo to Cart" : "Unavailable"}
                  </>
                )}
              </Button>
              
              <div className="flex gap-3">
                <Button variant="outline" className="flex-1">
                  <Heart className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="outline" className="flex-1">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
