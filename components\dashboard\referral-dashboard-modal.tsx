"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { User } from "@/utils/types";
import { PartnerQRCode } from "@/components/ui/qr-code";
import { 
  Copy, 
  Facebook, 
  Instagram, 
  TrendingUp, 
  Users, 
  DollarSign,
  Calendar,
  Award,
  Target,
  MessageSquare,
  X
} from "lucide-react";
import SpinnerCircle4 from "../customized/spinner/spinner-10";

interface ReferralDashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
}

interface PartnerData {
  id: string;
  name: string;
  surname: string;
  email: string;
  cellNumber: string;
  otherCellNumber?: string;
  referralCode: string;
  discountAmount: number;
  discountCode: string;
  isActive: boolean;
  commissionEarned: number;
  bonusPaid: number;
  createdAt: string;
  referralOrders: Array<{
    id: string;
    customerName: string;
    orderValue: number;
    commission: number;
    createdAt: string;
  }>;
}

const ReferralDashboardModal: React.FC<ReferralDashboardModalProps> = ({
  isOpen,
  onClose,
  user,
}) => {
  const [partner, setPartner] = useState<PartnerData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (isOpen && user.isPartner) {
      fetchPartnerData();
    }
  }, [isOpen, user.isPartner]);

  const fetchPartnerData = async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch("/api/partner/dashboard");
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch partner data");
      }
      
      if (data.partner) {
        setPartner(data.partner);
      } else {
        setError("Partner data not found");
      }
    } catch (err) {
      console.error("Error fetching partner data:", err);
      setError(err instanceof Error ? err.message : "Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  if (!user.isPartner) {
    return null;
  }

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center p-8">
            <SpinnerCircle4 />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <X className="h-5 w-5 text-red-500" />
              Error Loading Dashboard
            </DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={fetchPartnerData} variant="outline">
                Try Again
              </Button>
              <Button onClick={onClose}>Close</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!partner) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Not a Sales Partner</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-gray-600 mb-4">
              You are not currently registered as a sales partner.
            </p>
            <Button onClick={onClose}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Calculate statistics
  const totalReferrals = partner.referralOrders?.length || 0;
  const progress = Math.min((totalReferrals / 10) * 100, 100);
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  const monthlyOrders = (partner.referralOrders || []).filter((order) => {
    const d = new Date(order.createdAt);
    return d.getMonth() === currentMonth && d.getFullYear() === currentYear;
  });
  
  const monthlyCommission = monthlyOrders.reduce((sum, o) => sum + o.commission, 0);

  // Performance tip
  let performanceTip = "";
  if (totalReferrals < 10) {
    const toGo = 10 - totalReferrals;
    performanceTip = `You're only ${toGo} referral${toGo === 1 ? "" : "s"} away from your M300 bonus! Share your code to reach the milestone.`;
  } else {
    performanceTip = "You've unlocked your bonus! Keep referring to earn even more.";
  }

  // Best month calculation
  const monthStats: Record<string, { count: number; commission: number }> = {};
  (partner.referralOrders || []).forEach((order) => {
    const d = new Date(order.createdAt);
    const key = `${d.getFullYear()}-${d.getMonth()}`;
    if (!monthStats[key]) monthStats[key] = { count: 0, commission: 0 };
    monthStats[key].count++;
    monthStats[key].commission += order.commission;
  });
  
  let bestMonth = null;
  let bestMonthKey = "";
  for (const key in monthStats) {
    if (!bestMonth || monthStats[key].count > bestMonth.count) {
      bestMonth = monthStats[key];
      bestMonthKey = key;
    }
  }
  
  let bestMonthLabel = "";
  if (bestMonth) {
    const [year, month] = bestMonthKey.split("-");
    const date = new Date(Number(year), Number(month));
    bestMonthLabel = `${date.toLocaleString("default", { month: "long" })} ${year}`;
  }

  // Share links
  const referralUrl = `https://rivvsneakers.shop/?ref=${partner.referralCode}&discount=${partner.discountCode}`;
  const fbShare = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralUrl)}`;
  const igShare = `https://www.instagram.com/?url=${encodeURIComponent(referralUrl)}`;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Users className="h-6 w-6 text-blue-600" />
            My Referral Dashboard
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 p-2">
          {/* Personal Information & Key Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Your Referral Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="font-semibold">Your Referral Code:</div>
                  <div className="flex items-center gap-2 mt-1">
                    <code className="bg-gray-100 px-2 py-1 rounded text-lg font-mono">
                      {partner.referralCode}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(partner.referralCode)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="mt-2 space-y-1">
                    <div className="text-sm text-gray-600">
                      Discount Code:{" "}
                      <code className="bg-green-100 px-1 rounded font-mono">
                        {partner.discountCode}
                      </code>
                    </div>
                    <div className="text-sm text-gray-600">
                      Discount Amount:{" "}
                      <strong>M{partner.discountAmount?.toFixed(2) || "0.00"}</strong>
                    </div>
                    <div className="text-sm text-gray-600">
                      Status:{" "}
                      <Badge variant={partner.isActive ? "default" : "secondary"}>
                        {partner.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-700">
                      M{partner.commissionEarned?.toFixed(2) || "0.00"}
                    </div>
                    <div className="text-sm text-gray-600">Total Commission</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-700">
                      M{partner.bonusPaid?.toFixed(2) || "0.00"}
                    </div>
                    <div className="text-sm text-gray-600">Bonus Paid</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-700">
                      {totalReferrals}
                    </div>
                    <div className="text-sm text-gray-600">Total Referrals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-700">
                      {monthlyOrders.length}
                    </div>
                    <div className="text-sm text-gray-600">This Month</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Your QR Code</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex justify-center">
                  <PartnerQRCode
                    referralCode={partner.referralCode}
                    discountCode={partner.discountCode}
                    discountAmount={partner.discountAmount || 0}
                    partnerName={`${partner.name} ${partner.surname}`}
                    size={200}
                    showDownload={true}
                    showRefresh={true}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Progress Bar */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Progress toward 10-referral bonus (M300)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {totalReferrals} / 10 referrals
                  </span>
                  <span className="text-sm font-semibold">
                    {progress.toFixed(0)}%
                  </span>
                </div>
                <Progress value={progress} className="h-3" />
                {progress === 100 && (
                  <div className="text-green-700 font-semibold">
                    <Award className="inline h-4 w-4 mr-1" />
                    Congratulations! You've reached the bonus milestone.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  This Month's Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-700">
                      {monthlyOrders.length}
                    </div>
                    <div className="text-sm text-gray-700">Referrals this month</div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-700">
                      M{monthlyCommission.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-700">Commission this month</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Best Month Ever */}
            {bestMonth && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    Best Month Ever
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="font-semibold text-green-800 mb-2">
                      {bestMonthLabel}
                    </div>
                    <div className="text-green-700">
                      {bestMonth.count} referrals • M{bestMonth.commission.toFixed(2)} commission
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Performance Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Referral Performance Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-blue-800">{performanceTip}</div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Share */}
          <Card>
            <CardHeader>
              <CardTitle>Share Your Referral Link</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={referralUrl}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(referralUrl)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(fbShare, "_blank")}
                  >
                    <Facebook className="h-4 w-4 mr-1" />
                    Facebook
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(igShare, "_blank")}
                  >
                    <Instagram className="h-4 w-4 mr-1" />
                    Instagram
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Referred Orders Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Recent Referral Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              {partner.referralOrders && partner.referralOrders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 px-2">Customer</th>
                        <th className="text-left py-2 px-2">Order Value</th>
                        <th className="text-left py-2 px-2">Commission</th>
                        <th className="text-left py-2 px-2">Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {partner.referralOrders.slice(0, 10).map((order) => (
                        <tr key={order.id} className="border-b hover:bg-gray-50">
                          <td className="py-2 px-2 font-medium">
                            {order.customerName}
                          </td>
                          <td className="py-2 px-2">
                            M{order.orderValue.toFixed(2)}
                          </td>
                          <td className="py-2 px-2 text-green-600 font-semibold">
                            M{order.commission.toFixed(2)}
                          </td>
                          <td className="py-2 px-2 text-gray-600">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {partner.referralOrders.length > 10 && (
                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-600">
                        Showing 10 most recent orders out of {partner.referralOrders.length} total
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No referred orders yet.</p>
                  <p className="text-sm text-gray-500 mt-1">
                    Start sharing your referral code to earn commissions!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReferralDashboardModal;
