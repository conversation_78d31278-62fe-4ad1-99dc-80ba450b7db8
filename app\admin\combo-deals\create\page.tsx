"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Plus, X, Search } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { UserRole } from "@/utils/types";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { toast } from "react-hot-toast";
import { generateComboImage, uploadComboImage } from "@/lib/combo-image-utils";

type Product = {
  id: string;
  name: string;
  brand: string;
  price: number;
  discountedPrice?: number;
  images: string[];
  stock: number;
};

export default function CreateComboPage() {
  const router = useRouter();
  const { data: session, isPending } = authClient.useSession();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showProductSearch, setShowProductSearch] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    comboPrice: "",
    validUntil: "",
    isActive: true,
    isFeatured: false,
  });
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string>("");
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  useEffect(() => {
    if (!isPending && (!session?.user || (session.user as any).role !== UserRole.ADMIN)) {
      router.push("/");
      return;
    }
    if (session?.user) {
      fetchProducts();
    }
  }, [session, isPending, router]);

  const fetchProducts = async () => {
    try {
      const response = await fetch("/api/admin/products?limit=100");
      const result = await response.json();
      if (result.success) {
        setProducts(result.data.products);
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    }
  };

  const filteredProducts = products.filter(
    (product) =>
      !selectedProducts.find((sp) => sp.id === product.id) &&
      (product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const addProduct = (product: Product) => {
    setSelectedProducts([...selectedProducts, product]);
    setSearchTerm("");
    setShowProductSearch(false);
  };

  const removeProduct = (productId: string) => {
    setSelectedProducts(selectedProducts.filter((p) => p.id !== productId));
  };

  const calculatePrices = () => {
    const originalPrice = selectedProducts.reduce(
      (sum, product) => sum + (product.discountedPrice || product.price),
      0
    );
    const comboPrice = parseFloat(formData.comboPrice) || 0;
    const discount = originalPrice - comboPrice;
    const discountPercent = originalPrice > 0 ? (discount / originalPrice) * 100 : 0;

    return { originalPrice, discount, discountPercent };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedProducts.length === 0) {
      toast.error("Please select at least one product");
      return;
    }

    if (!formData.name || !formData.comboPrice || !formData.validUntil) {
      toast.error("Please fill in all required fields");
      return;
    }

    const { originalPrice } = calculatePrices();
    const comboPrice = parseFloat(formData.comboPrice);

    if (comboPrice >= originalPrice) {
      toast.error("Combo price must be less than original price");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/combo-deals", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          comboPrice,
          productIds: selectedProducts.map((p) => p.id),
        }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success("Combo deal created successfully!");
        router.push("/admin/combo-deals");
      } else {
        toast.error(result.error || "Failed to create combo deal");
      }
    } catch (error) {
      console.error("Error creating combo:", error);
      toast.error("Failed to create combo deal");
    } finally {
      setLoading(false);
    }
  };

  const { originalPrice, discount, discountPercent } = calculatePrices();

  const generateImage = async () => {
    if (selectedProducts.length === 0 || !formData.name || !formData.comboPrice) {
      toast.error("Please complete the form before generating image");
      return;
    }

    setIsGeneratingImage(true);
    try {
      const products = selectedProducts.map(product => ({
        url: product.images[0] || "/placeholder-product.png",
        name: product.name,
        brand: product.brand,
        price: product.discountedPrice || product.price,
      }));

      const imageUrl = await generateComboImage(
        products,
        formData.name,
        originalPrice,
        parseFloat(formData.comboPrice)
      );

      setGeneratedImageUrl(imageUrl);
      toast.success("Combo image generated successfully!");
    } catch (error) {
      console.error("Error generating image:", error);
      toast.error("Failed to generate combo image");
    } finally {
      setIsGeneratingImage(false);
    }
  };

  if (isPending) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-40">
          <SpinnerCircle4 />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Create Combo Deal</h1>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Combo Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Nike & Adidas Bundle"
                  required
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe this combo deal..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="validUntil">Valid Until *</Label>
                  <Input
                    id="validUntil"
                    type="datetime-local"
                    value={formData.validUntil}
                    onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="comboPrice">Combo Price (M) *</Label>
                  <Input
                    id="comboPrice"
                    type="number"
                    step="0.01"
                    value={formData.comboPrice}
                    onChange={(e) => setFormData({ ...formData, comboPrice: e.target.value })}
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, isActive: checked as boolean })
                    }
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => 
                      setFormData({ ...formData, isFeatured: checked as boolean })
                    }
                  />
                  <Label htmlFor="isFeatured">Featured</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Select Products</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add Product Button */}
              <div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowProductSearch(!showProductSearch)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
              </div>

              {/* Product Search */}
              {showProductSearch && (
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {filteredProducts.map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => addProduct(product)}
                      >
                        <div className="flex items-center gap-3">
                          {product.images[0] && (
                            <img
                              src={product.images[0]}
                              alt={product.name}
                              className="w-12 h-12 object-cover rounded"
                            />
                          )}
                          <div>
                            <p className="font-medium">{product.brand} {product.name}</p>
                            <p className="text-sm text-gray-600">
                              M{product.discountedPrice || product.price}
                            </p>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          Add
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Selected Products */}
              {selectedProducts.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">Selected Products ({selectedProducts.length})</h4>
                  <div className="space-y-2">
                    {selectedProducts.map((product) => (
                      <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {product.images[0] && (
                            <img
                              src={product.images[0]}
                              alt={product.name}
                              className="w-12 h-12 object-cover rounded"
                            />
                          )}
                          <div>
                            <p className="font-medium">{product.brand} {product.name}</p>
                            <p className="text-sm text-gray-600">
                              M{product.discountedPrice || product.price}
                            </p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeProduct(product.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Price Summary */}
          {selectedProducts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Price Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Original Price:</span>
                    <span>M{originalPrice.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Combo Price:</span>
                    <span className="font-bold text-green-600">
                      M{parseFloat(formData.comboPrice || "0").toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>Savings:</span>
                    <span>M{discount.toFixed(2)} ({discountPercent.toFixed(1)}% off)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Image Generation */}
          {selectedProducts.length > 0 && formData.name && formData.comboPrice && (
            <Card>
              <CardHeader>
                <CardTitle>Combo Image</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <Button
                    type="button"
                    onClick={generateImage}
                    disabled={isGeneratingImage}
                    variant="outline"
                  >
                    {isGeneratingImage ? (
                      <>
                        <SpinnerCircle4 />
                        Generating...
                      </>
                    ) : (
                      "Generate Combo Image"
                    )}
                  </Button>
                </div>

                {generatedImageUrl && (
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Generated combo image:</p>
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <img
                        src={generatedImageUrl}
                        alt="Generated combo image"
                        className="max-w-full h-auto rounded-lg shadow-sm"
                        style={{ maxHeight: "300px" }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Submit Button */}
          <div className="flex gap-4">
            <Button type="submit" disabled={loading || selectedProducts.length === 0}>
              {loading ? <SpinnerCircle4 /> : "Create Combo Deal"}
            </Button>
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
