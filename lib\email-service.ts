import { sendEmail, emailTemplates, EMAIL_CONFIG } from "./email";
import { Order, ContactMessage, User } from "@/utils/types";
import { formatPrice } from "./product-utils";
import { ORDER_STATUS } from '@/utils/constants';
import { UserRole } from '@prisma/client';

// Type for order status update email - more flexible than full Order type
type OrderForStatusUpdate = {
  id: string;
  orderNumber: string;
  totalAmount: number;
  user?: {
    id: string;
    name: string;
    email: string;
  } | null;
  orderItems: Array<{
    quantity: number;
    price: number;
    size: string | null;
    product: {
      name: string;
    };
  }>;
};

// Send order confirmation email
export async function sendOrderConfirmationEmail(order: Order) {
  try {
    const recipient = order.user?.email || "";
    console.log(`[EMAIL] Attempting to send order confirmation to customer: ${recipient} (Order: ${order.orderNumber})`);
    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));
    const template = emailTemplates.orderConfirmation({
      customerName: order.user?.name || "Customer",
      orderNumber: order.orderNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      phoneNumber: order.phoneNumber,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/orders/${order.id}`,
    });
    const result = await sendEmail({
      to: recipient,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
    if (result.success) {
      console.log(`[EMAIL] Order confirmation sent to customer: ${recipient} (Order: ${order.orderNumber})`);
    } else {
      console.error(`[EMAIL] Failed to send order confirmation to customer: ${recipient} (Order: ${order.orderNumber})`, result.error);
      // Fallback: notify admin
      const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
      await sendEmail({
        to: adminEmail,
        subject: `[FALLBACK] Failed to send order confirmation to customer for order ${order.orderNumber}`,
        html: `<p>Failed to send order confirmation to customer: ${recipient} for order ${order.orderNumber}.<br/>Error: ${result.error}</p>` + template.html,
        text: `Failed to send order confirmation to customer: ${recipient} for order ${order.orderNumber}. Error: ${result.error}\n` + template.text,
      });
    }
    return result;
  } catch (error) {
    console.error("Error sending order confirmation email:", error);
    // Fallback: notify admin
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    await sendEmail({
      to: adminEmail,
      subject: `[FALLBACK] Exception sending order confirmation to customer for order ${(order as any)?.orderNumber}`,
      html: `<p>Exception sending order confirmation to customer for order ${(order as any)?.orderNumber}.<br/>Error: ${error}</p>` || "",
      text: `Exception sending order confirmation to customer for order ${(order as any)?.orderNumber}. Error: ${error}`,
    });
    return { success: false, error: "Failed to send order confirmation email" };
  }
}

// Send admin order notification email
export async function sendAdminOrderNotification(order: Order, paymentMethod?: string) {
  try {
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    console.log(`[EMAIL] Attempting to send admin order notification to: ${adminEmail} (Order: ${order.orderNumber})`);
    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));
    const template = emailTemplates.adminOrderNotification({
      orderNumber: order.orderNumber,
      customerName: order.user?.name || "Customer",
      customerEmail: order.user?.email || "",
      customerPhone: order.phoneNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      paymentMethod,
      notes: order.notes || undefined,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/admin/orders/${order.id}`,
      createdAt: new Date(order.createdAt).toLocaleString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
    });
    const result = await sendEmail({
      to: adminEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
    if (result.success) {
      console.log(`[EMAIL] Admin order notification sent to: ${adminEmail} (Order: ${order.orderNumber})`);
    } else {
      console.error(`[EMAIL] Failed to send admin order notification to: ${adminEmail} (Order: ${order.orderNumber})`, result.error);
    }
    return result;
  } catch (error) {
    console.error("[ADMIN EMAIL] Error sending admin order notification:", error);
    return { success: false, error: "Failed to send admin order notification" };
  }
}

/**
 * Send delivery notification email to Delva
 */
export async function sendDelvaDeliveryNotification(order: Order) {
  try {
    const delvaEmail = "<EMAIL>";

    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || "N/A",
      color: item.color || "N/A",
      image: item.product.images?.[0] || "",
    }));

    const template = emailTemplates.delvaDeliveryNotification({
      customerName: order.user?.name || "Customer",
      customerEmail: order.user?.email || "",
      customerPhone: order.phoneNumber,
      orderNumber: order.orderNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      notes: order.notes || undefined,
      orderId: order.id,
      createdAt: new Date(order.createdAt).toLocaleString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
      // locationEnabled: order.locationEnabled,
      // latitude: order.customerLatitude || undefined,
      // longitude: order.customerLongitude || undefined,
    });

    const result = await sendEmail({
      to: delvaEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    // If email was sent successfully, update the order to mark Delva as notified
    if (result.success) {
      try {
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();

        await prisma.order.update({
          where: { id: order.id },
          data: {
            delvaNotified: true,
            delvaNotifiedAt: new Date(),
          },
        });

        await prisma.$disconnect();
      } catch (dbError) {
        console.error("Error updating Delva notification status:", dbError);
        // Don't fail the email sending if database update fails
      }
    }

    return result;
  } catch (error) {
    console.error("Error sending Delva delivery notification:", error);
    return { success: false, error: "Failed to send Delva delivery notification" };
  }
}

/**
 * Send order status update email
 */
export async function sendOrderStatusUpdateEmail(order: OrderForStatusUpdate, newStatus: string, trackingInfo?: string, cancellationReason?: string) {
  try {
    if (!order.user?.email) {
      throw new Error('Customer email not found');
    }

    const orderItems = order.orderItems.map((item: any) => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || "N/A",
      image: item.product.images?.[0] || "",
    }));

    const template = emailTemplates.orderStatusUpdate({
      customerName: order.user.name,
      orderNumber: order.orderNumber,
      status: newStatus,
      statusMessage: getStatusMessage(newStatus),
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/orders/${order.id}`,
      trackingInfo,
      estimatedDelivery: getEstimatedDelivery(newStatus),
      cancellationReason,
    });

    const result = await sendEmail({
      to: order.user.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending order status update email:", error);
    return { success: false, error: "Failed to send order status update email" };
  }
}

function getStatusMessage(status: string): string {
  const messages: Record<string, string> = {
    PENDING: "Your order is pending confirmation.",
    PAID: "Your payment has been confirmed!",
    CONFIRMED: "Your order has been confirmed and is being prepared.",
    PROCESSING: "Your order is currently being processed.",
    SHIPPED: "Your order has been shipped and is on its way!",
    DELIVERED: "Your order has been delivered successfully!",
    CANCELLED: "Your order has been cancelled.",
  };
  return messages[status] || "Your order status has been updated.";
}

function getEstimatedDelivery(status: string): string | undefined {
  if (status === "SHIPPED") {
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + 2); // 2 days from shipping
    return deliveryDate.toLocaleDateString('en-LS', {
      timeZone: 'Africa/Maseru',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
  return undefined;
}

/**
 * Send Lay-Buy reminder email
 */
export async function sendLayBuyReminder(
  layBuyOrder: any,
  reminderType: 'WEEKLY' | 'URGENT' | 'GRACE_PERIOD' | 'FINAL_NOTICE',
  weekNumber: number
) {
  try {
    if (!layBuyOrder.user?.email) {
      throw new Error('Customer email not found');
    }

    const dueDate = new Date(layBuyOrder.dueDate);
    const gracePeriodEnd = new Date(layBuyOrder.gracePeriodEnd);
    const now = new Date();

    let daysRemaining: number;
    if (reminderType === 'GRACE_PERIOD' || reminderType === 'FINAL_NOTICE') {
      daysRemaining = Math.ceil((gracePeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    } else {
      daysRemaining = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    }

    const remainingAmount = layBuyOrder.totalAmount - layBuyOrder.amountPaid;

    const emailData = {
      customerName: layBuyOrder.user.name,
      orderNumber: layBuyOrder.orderNumber,
      remainingAmount: formatPrice(remainingAmount),
      dueDate: dueDate.toLocaleDateString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      gracePeriodEnd: gracePeriodEnd.toLocaleDateString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      daysRemaining: Math.max(0, daysRemaining),
      weekNumber,
      paymentInstructions: `
        <p><strong>Payment Methods:</strong></p>
        <ul>
          <li><strong>M-Pesa:</strong> Send payment to +266 5316 3354 (Katleho Namane)</li>
          <li><strong>EcoCash:</strong> Send payment to +266 6284 4473 (Katleho Namane)</li>
          <li><strong>Bank Transfer:</strong> Contact us at +266 6284 4473 or <EMAIL> for banking details</li>
        </ul>
        <p><strong>Reference:</strong> Include your order number ${layBuyOrder.orderNumber} in the payment reference.</p>
        <p><strong>Proof:</strong> After payment, please upload proof via your order page or WhatsApp us at +266 6284 4473.</p>
      `,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy-orders/${layBuyOrder.id}`,
    };

    let template;
    switch (reminderType) {
      case 'WEEKLY':
        template = emailTemplates.layBuyWeeklyReminder(emailData);
        break;
      case 'URGENT':
        template = emailTemplates.layBuyUrgentReminder(emailData);
        break;
      case 'GRACE_PERIOD':
      case 'FINAL_NOTICE':
        template = emailTemplates.layBuyGracePeriodReminder(emailData);
        break;
      default:
        throw new Error('Invalid reminder type');
    }

    const result = await sendEmail({
      to: layBuyOrder.user.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error('Error sending Lay-Buy reminder:', error);
    return { success: false, error: 'Failed to send Lay-Buy reminder' };
  }
}

/**
 * Send Lay-Buy order confirmation email
 */
export async function sendLayBuyOrderConfirmation(layBuyOrder: any) {
  try {
    if (!layBuyOrder.user?.email) {
      throw new Error('Customer email not found');
    }

    const dueDate = new Date(layBuyOrder.dueDate);
    const orderItems = layBuyOrder.orderItems.map((item: any) => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size,
      color: item.color,
    }));

    const template = emailTemplates.orderConfirmation({
      customerName: layBuyOrder.user.name,
      orderNumber: layBuyOrder.orderNumber,
      orderTotal: formatPrice(layBuyOrder.totalAmount),
      upfrontAmount: formatPrice(layBuyOrder.upfrontAmount),
      remainingAmount: formatPrice(layBuyOrder.remainingAmount),
      dueDate: dueDate.toLocaleDateString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      orderItems,
      shippingAddress: layBuyOrder.shippingAddress,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy-orders/${layBuyOrder.id}`,
      isLayBuy: true,
    });

    const result = await sendEmail({
      to: layBuyOrder.user.email,
      subject: `🎉 Lay-Buy Order Confirmed - ${layBuyOrder.orderNumber} | RIVV`,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error('Error sending Lay-Buy order confirmation:', error);
    return { success: false, error: 'Failed to send order confirmation' };
  }
}

// Send contact form confirmation email
export async function sendContactConfirmationEmail(contactMessage: ContactMessage) {
  try {
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };

    const template = emailTemplates.contactConfirmation({
      customerName: contactMessage.name,
      subject: subjectMap[contactMessage.subject] || contactMessage.subject,
    });

    const result = await sendEmail({
      to: contactMessage.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending contact confirmation email:", error);
    return { success: false, error: "Failed to send contact confirmation email" };
  }
}

// Send new contact message notification to admin
export async function sendNewContactMessageNotification(contactMessage: ContactMessage) {
  try {
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };

    const template = emailTemplates.newContactMessage({
      customerName: contactMessage.name,
      customerEmail: contactMessage.email,
      subject: subjectMap[contactMessage.subject] || contactMessage.subject,
      message: contactMessage.message,
      messageUrl: `${EMAIL_CONFIG.baseUrl}/admin/contact-messages/${contactMessage.id}`,
    });

    const result = await sendEmail({
      to: adminEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending new contact message notification:", error);
    return { success: false, error: "Failed to send admin notification" };
  }
}

// Send password reset email
export async function sendPasswordResetEmail(email: string, name: string, resetToken: string) {
  try {
    const resetUrl = `${EMAIL_CONFIG.baseUrl}/reset-password?token=${resetToken}`;

    const template = emailTemplates.passwordReset({
      customerName: name,
      resetUrl,
    });

    const result = await sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return { success: false, error: "Failed to send password reset email" };
  }
}

// Send welcome email for new users
export async function sendWelcomeEmail(email: string, name: string) {
  try {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Rivv</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
            .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Rivv!</h1>
            </div>
            
            <p>Hi ${name},</p>
            
            <p>Welcome to Rivv E-commerce! We're excited to have you as part of our community.</p>
            
            <p>Here's what you can do with your new account:</p>
            <ul>
              <li>Browse our extensive product catalog</li>
              <li>Save items to your wishlist</li>
              <li>Track your orders in real-time</li>
              <li>Enjoy exclusive member discounts</li>
              <li>Get priority customer support</li>
            </ul>
            
            <a href="${EMAIL_CONFIG.baseUrl}/products" class="button">Start Shopping</a>
            
            <p>If you have any questions, our support team is here to <NAME_EMAIL></p>
            
            <div class="footer">
              <p>Thank you for choosing Rivv!</p>
              <p>© 2024 Rivv E-commerce. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Welcome to Rivv!
      
      Hi ${name},
      
      Welcome to Rivv E-commerce! We're excited to have you as part of our community.
      
      Here's what you can do with your new account:
      - Browse our extensive product catalog
      - Save items to your wishlist
      - Track your orders in real-time
      - Enjoy exclusive member discounts
      - Get priority customer support
      
      Start shopping: ${EMAIL_CONFIG.baseUrl}/products
      
      If you have any questions, our support team is here to <NAME_EMAIL>
      
      Thank you for choosing Rivv!
    `;

    const result = await sendEmail({
      to: email,
      subject: "Welcome to Rivv - Your Account is Ready!",
      html,
      text,
    });

    return result;
  } catch (error) {
    console.error("Error sending welcome email:", error);
    return { success: false, error: "Failed to send welcome email" };
  }
}

/**
 * Send order details to Delva
 */
export async function sendDelvaOrderEmail(order: Order) {
  try {
    const delvaEmail = "<EMAIL>";

    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || "N/A",
      color: item.color || "N/A",
      image: item.product.images?.[0] || "",
    }));

    const template = emailTemplates.delvaDeliveryNotification({
      customerName: order.user?.name || "Customer",
      customerEmail: order.user?.email || "",
      customerPhone: order.phoneNumber,
      orderNumber: order.orderNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      notes: order.notes || undefined,
      orderId: order.id,
      createdAt: new Date(order.createdAt).toLocaleString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
      // locationEnabled: false,
      // latitude: undefined,
      // longitude: undefined,
    });

    const result = await sendEmail({
      to: delvaEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    // If email was sent successfully, update the order to mark Delva as notified
    if (result.success) {
      try {
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();

        await prisma.order.update({
          where: { id: order.id },
          data: {
            delvaNotified: true,
            delvaNotifiedAt: new Date(),
          },
        });

        await prisma.$disconnect();
      } catch (dbError) {
        console.error("Error updating Delva notification status:", dbError);
        // Don't fail the email sending if database update fails
      }
    }

    return result;
  } catch (error) {
    console.error("Error sending Delva order email:", error);
    return { success: false, error: "Failed to send Delva order email" };
  }
}

export async function sendPartnerWelcomeEmail({ name, email, dashboardUrl }: { name: string; email: string; dashboardUrl: string }) {
  try {
    const template = emailTemplates.partnerWelcome({ name, dashboardUrl });
    const result = await sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
    return result;
  } catch (error) {
    console.error("Error sending partner welcome email:", error);
    return { success: false, error: "Failed to send partner welcome email" };
  }
}

// Send partner referral notification email
export async function sendPartnerReferralNotification(order: Order, partner: any) {
  try {
    const recipient = partner.email;
    console.log(`[EMAIL] Attempting to send partner referral notification to: ${recipient} (Order: ${order.orderNumber})`);
    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));

    // Calculate order value and commission robustly
    const orderValueRaw = Array.isArray(order.orderItems)
      ? order.orderItems.reduce((sum, item) => sum + (Number(item.price) * Number(item.quantity)), 0)
      : 0;
    const commissionRaw = Math.round(orderValueRaw * 0.05 * 100) / 100;
    const orderValue = formatPrice(orderValueRaw);
    const commission = formatPrice(commissionRaw);

    const template = emailTemplates.partnerReferralNotification({
      partnerName: partner.name,
      customerName: order.user?.name || "Customer",
      orderNumber: order.orderNumber,
      orderValue,
      commission,
      orderItems,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/admin/orders/${order.id}`,
      createdAt: new Date(order.createdAt).toLocaleString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    });

    const result = await sendEmail({
      to: recipient,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    if (result.success) {
      console.log(`[EMAIL] Partner referral notification sent to: ${recipient} (Order: ${order.orderNumber})`);
    } else {
      console.error(`[EMAIL] Failed to send partner referral notification to: ${recipient} (Order: ${order.orderNumber})`, result.error);
      // Fallback: notify admin
      const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
      await sendEmail({
        to: adminEmail,
        subject: `[FALLBACK] Failed to send partner referral notification for order ${order.orderNumber}`,
        html: `<p>Failed to send partner referral notification to: ${recipient} for order ${order.orderNumber}.<br/>Error: ${result.error}</p>` + template.html,
        text: `Failed to send partner referral notification to: ${recipient} for order ${order.orderNumber}. Error: ${result.error}\n` + template.text,
      });
    }
    return result;
  } catch (error) {
    console.error("Error sending partner referral notification:", error);
    // Fallback: notify admin
    const adminEmail = process.env.ADMIN_EMAIL || "<EMAIL>";
    await sendEmail({
      to: adminEmail,
      subject: `[FALLBACK] Exception sending partner referral notification for order ${(order as any)?.orderNumber}`,
      html: `<p>Exception sending partner referral notification for order ${(order as any)?.orderNumber}.<br/>Error: ${error}</p>` || "",
      text: `Exception sending partner referral notification for order ${(order as any)?.orderNumber}. Error: ${error}`,
    });
    return { success: false, error: "Failed to send partner referral notification" };
  }
}

// --- TEST EMAIL FUNCTIONS ---

// Helper: create a mock product object
function createMockProduct() {
  return {
    id: 'test-product-id',
    name: 'Test Sneaker',
    description: 'A premium test sneaker',
    price: 999.99,
    discountedPrice: null,
    costPrice: 500,
    shippingFee: 100,
    lateCollectionFee: 10,
    totalCost: 610,
    brand: 'TestBrand',
    categoryId: 'test-category-id',
    images: ['https://via.placeholder.com/150'],
    sizes: ['9'],
    colors: ['Black'],
    stock: 10,
    isActive: true,
    rating: 5,
    reviewCount: 1,
    costPriceUpdatedAt: null,
    costPriceUpdatedBy: null,
    feesUpdatedAt: null,
    feesUpdatedBy: null,
    aiAnalysis: null,
    enhancedBy: null,
    enhancementStatus: null,
    imageAngles: null,
    imageSource: null,
    lastEnhancedAt: null,
    qualityScore: null,

    createdAt: new Date(),
    updatedAt: new Date(),
    category: { id: 'test-category-id', name: 'Test Category', description: 'Test', image: null, isActive: true, createdAt: new Date(), updatedAt: new Date(), products: [] },
    reviews: [],
    _count: { reviews: 0, orderItems: 0 },
    isFeatured: false,
    orderItems: [],
    layBuyOrderItems: [],
  };
}

// Helper: create a mock order item
function createMockOrderItem() {
  return {
    id: 'test-order-item-id',
    orderId: 'test-order-id',
    productId: 'test-product-id',
    quantity: 1,
    price: 999.99,
    costPrice: 500,
    shippingFee: 100,
    lateCollectionFee: 10,
    totalCost: 610,
    profit: 389.99,
    size: '9',
    color: 'Black',
    createdAt: new Date(),
    product: createMockProduct(),
  };
}

// Helper: create a mock user
function createMockUser() {
  return {
    id: 'test-user-id',
    name: 'Test User',
    emailVerified: true,
    email: '<EMAIL>',
    role: UserRole.USER,
    createdAt: new Date(),
    updatedAt: new Date(),
    image: null,
  };
}

// Helper: create a mock order object
function createMockOrder() {
  return {
    id: 'test-order-id',
    userId: 'test-user-id',
    orderNumber: 'RIVV-TEST-1234',
    status: ORDER_STATUS.PENDING,
    totalAmount: 999.99,
    totalCostPrice: 610,
    totalProfit: 389.99,
    discountAmount: 0,
    discountCodeId: null,
    shippingAddress: '123 Test Street, Maseru',
    phoneNumber: '+266 60000000',
    notes: 'Test order notes',
    createdAt: new Date(),
    updatedAt: new Date(),
    adminNotes: null,
    trackingNumber: null,
    trackingUrl: null,
    delvaNotified: false,
    delvaNotifiedAt: null,
    deliveryFee: 90,
    deliveryFeePaid: false,
    deliveryFeePaidAt: null,
    deliveredAt: null,
    isBulkDelivery: false,
    paymentProof: null,
    deliveryRecords: [],
    customerLatitude: null,
    customerLongitude: null,
    locationUpdatedAt: null,
    locationEnabled: false,
    user: createMockUser(),
    orderItems: [createMockOrderItem()],
    discountCode: null,
  };
}

export async function sendTestCustomerOrderEmail() {
  const order = createMockOrder();
  return sendOrderConfirmationEmail(order);
}

export async function sendTestAdminOrderEmail() {
  const order = createMockOrder();
  return sendAdminOrderNotification(order);
}

export async function sendTestDelvaOrderEmail() {
  const order = createMockOrder();
  return sendDelvaDeliveryNotification(order);
}

export async function sendTestPartnerReferralEmail() {
  const order = createMockOrder();
  const partner = { name: 'Test Partner', email: '<EMAIL>' };
  return sendPartnerReferralNotification(order, partner);
}

export async function sendLayBuyPaymentVerificationEmail({
  to,
  customerName,
  orderNumber,
  paymentAmount,
  remainingBalance,
  isCompleted,
  orderUrl
}: {
  to: string;
  customerName: string;
  orderNumber: string;
  paymentAmount: string;
  remainingBalance: string;
  isCompleted: boolean;
  orderUrl: string;
}) {
  try {
    const subject = isCompleted
      ? `🎉 Lay-Buy Payment Complete - Order ${orderNumber} | RIVV`
      : `✅ Payment Verified - Order ${orderNumber} | RIVV`;
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset='utf-8'>
          <meta name='viewport' content='width=device-width, initial-scale=1.0'>
          <title>Lay-Buy Payment Verification</title>
        </head>
        <body>
          <div style="text-align:center;margin-bottom:20px;"><img src='${EMAIL_CONFIG.baseUrl}/logo.jpeg' alt='RIVV Logo' style='max-width:180px;max-height:80px;'/></div>
          <div style='max-width:600px;margin:0 auto;font-family:Arial,sans-serif;'>
            <h2>${isCompleted ? '🎉 Payment Complete!' : '✅ Payment Verified'}</h2>
            <p>Hi ${customerName},</p>
            <p>Great news! Your payment of <strong>${paymentAmount}</strong> has been verified and applied to your Lay-Buy order <strong>${orderNumber}</strong>.</p>
            <div style='background:#f9fafb;padding:20px;border-radius:8px;margin:20px 0;'>
              <p><strong>Payment Amount:</strong> ${paymentAmount}</p>
              <p><strong>Remaining Balance:</strong> ${remainingBalance}</p>
            </div>
            ${isCompleted ? `<p>🎉 Congratulations! Your Lay-Buy order is now complete! We'll process your order and prepare it for delivery.</p>` : `<p>You still have <strong>${remainingBalance}</strong> remaining to complete your Lay-Buy order.</p>`}
            <div style='text-align:center;margin:30px 0;'>
              <a href='${orderUrl}' style='display:inline-block;background:#3b82f6;color:white;padding:12px 24px;text-decoration:none;border-radius:6px;'>View Order Details</a>
            </div>
            <p>Thank you for choosing RIVV!</p>
          </div>
        </body>
      </html>
    `;
    const text = `
      ${isCompleted ? '🎉 Payment Complete!' : '✅ Payment Verified'}\n\nHi ${customerName},\n\nGreat news! Your payment of ${paymentAmount} has been verified and applied to your Lay-Buy order.\n\nPayment Details:\n- Payment Amount: ${paymentAmount}\n- Remaining Balance: ${remainingBalance}\n\n${isCompleted ? `🎉 Congratulations! Your Lay-Buy order is now complete! We'll process your order and prepare it for delivery.` : `You still have ${remainingBalance} remaining to complete your Lay-Buy order.`}\n\nView order details: ${orderUrl}\n\nThank you for choosing RIVV!\n    `;
    return await sendEmail({ to, subject, html, text });
  } catch (error) {
    console.error('Error sending Lay-Buy payment verification email:', error);
    return { success: false, error: 'Failed to send payment verification email' };
  }
}

export async function sendLayBuyPaymentRejectedEmail({
  to,
  customerName,
  orderNumber,
  paymentAmount,
  rejectionReason,
  orderUrl
}: {
  to: string;
  customerName: string;
  orderNumber: string;
  paymentAmount: string;
  rejectionReason?: string;
  orderUrl: string;
}) {
  const subject = `Lay-Buy Payment Rejected - Order ${orderNumber} | RIVV`;
  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset='utf-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1'>
      </head>
      <body style='font-family:sans-serif;'>
        <div style="text-align:center;margin-bottom:20px;">
          <img src='${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/logo.jpeg' alt='RIVV Logo' style='max-width:180px;max-height:80px;'/>
        </div>
        <h2 style='color:#d32f2f;'>Your Lay-Buy Payment Was Rejected</h2>
        <p>Hi ${customerName},</p>
        <p>Your payment of <strong>${paymentAmount}</strong> for Lay-Buy order <strong>${orderNumber}</strong> was <span style='color:#d32f2f;font-weight:bold;'>rejected</span>.</p>
        ${rejectionReason ? `<p><strong>Reason:</strong> ${rejectionReason}</p>` : ''}
        <p>Please ensure you upload a <strong>valid payment proof</strong> (clear screenshot, correct reference, etc.).</p>
        <p><strong>Your Lay-Buy has been paused</strong> until a valid payment is provided and verified by our team.</p>
        <p>You can review your order and resubmit proof here:<br/>
          <a href='${orderUrl}' style='color:#1976d2;'>View My Lay-Buy Order</a>
        </p>
        <p>If you need help, reply to this email or contact our support team.</p>
        <p style='margin-top:32px;color:#888;'>Thank you,<br/>RIVV Team</p>
      </body>
    </html>
  `;
  return sendEmail({
    to,
    subject,
    html,
  });
}
