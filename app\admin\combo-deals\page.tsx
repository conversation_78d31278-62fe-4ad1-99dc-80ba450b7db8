"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/admin-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Edit, Trash2, Eye } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { UserRole } from "@/utils/types";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { toast } from "react-hot-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

type ComboDeal = {
  id: string;
  name: string;
  description?: string;
  originalPrice: number;
  comboPrice: number;
  discount: number;
  discountPercent: number;
  validUntil: string;
  isActive: boolean;
  isFeatured: boolean;
  createdAt: string;
  comboProducts: Array<{
    product: {
      id: string;
      name: string;
      brand: string;
      price: number;
      images: string[];
    };
  }>;
  _count: {
    cartItems: number;
    orderItems: number;
  };
};

export default function ComboDealsPage() {
  const router = useRouter();
  const { data: session, isPending } = authClient.useSession();
  const [combos, setCombos] = useState<ComboDeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  useEffect(() => {
    if (!isPending && (!session?.user || (session.user as any).role !== UserRole.ADMIN)) {
      router.push("/");
      return;
    }
    if (session?.user) {
      fetchCombos();
    }
  }, [session, isPending, router]);

  const fetchCombos = async () => {
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        limit: "50",
      });

      const response = await fetch(`/api/admin/combo-deals?${params}`);
      const result = await response.json();

      if (result.success) {
        setCombos(result.data.combos);
      } else {
        toast.error("Failed to fetch combo deals");
      }
    } catch (error) {
      console.error("Error fetching combos:", error);
      toast.error("Failed to fetch combo deals");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this combo deal?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/combo-deals/${id}`, {
        method: "DELETE",
      });
      const result = await response.json();

      if (result.success) {
        toast.success("Combo deal deleted successfully");
        fetchCombos();
      } else {
        toast.error(result.error || "Failed to delete combo deal");
      }
    } catch (error) {
      console.error("Error deleting combo:", error);
      toast.error("Failed to delete combo deal");
    }
  };

  const isExpired = (validUntil: string) => {
    return new Date(validUntil) < new Date();
  };

  if (isPending || loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-40">
          <SpinnerCircle4 />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Combo Deals</h1>
          <Button onClick={() => router.push("/admin/combo-deals/create")}>
            <Plus className="h-4 w-4 mr-2" />
            Create Combo Deal
          </Button>
        </div>

        {/* Filters */}
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search combo deals..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="expired">Expired</option>
          </select>
          <Button onClick={fetchCombos} variant="outline">
            Search
          </Button>
        </div>

        {/* Combo Deals Grid */}
        {combos.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No combo deals found</p>
              <Button onClick={() => router.push("/admin/combo-deals/create")}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Combo Deal
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {(combos || []).map((combo) => (
              <Card key={combo.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{combo.name}</CardTitle>
                    <div className="flex gap-1">
                      {combo.isFeatured && (
                        <Badge variant="secondary">Featured</Badge>
                      )}
                      <Badge
                        variant={
                          !combo.isActive
                            ? "destructive"
                            : isExpired(combo.validUntil)
                            ? "outline"
                            : "default"
                        }
                      >
                        {!combo.isActive
                          ? "Inactive"
                          : isExpired(combo.validUntil)
                          ? "Expired"
                          : "Active"}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Price Info */}
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-green-600">
                          M{combo.comboPrice}
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          M{combo.originalPrice}
                        </span>
                      </div>
                      <p className="text-sm text-green-600">
                        Save M{combo.discount} ({combo.discountPercent.toFixed(1)}% off)
                      </p>
                    </div>

                    {/* Products */}
                    <div>
                      <p className="text-sm font-medium mb-1">
                        Products ({combo.comboProducts.length}):
                      </p>
                      <div className="text-xs text-gray-600">
                        {(combo.comboProducts || []).map((cp, index) => (
                          <span key={cp.product.id}>
                            {cp.product.brand} {cp.product.name}
                            {index < combo.comboProducts.length - 1 && ", "}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Valid Until */}
                    <p className="text-sm text-gray-600">
                      Valid until: {new Date(combo.validUntil).toLocaleDateString()}
                    </p>

                    {/* Usage Stats */}
                    <div className="text-xs text-gray-500">
                      {combo._count.orderItems} orders • {combo._count.cartItems} in carts
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => router.push(`/admin/combo-deals/${combo.id}`)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => router.push(`/admin/combo-deals/${combo.id}/edit`)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="destructive"
                            disabled={combo._count.orderItems > 0}
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Combo Deal</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{combo.name}"? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDelete(combo.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
