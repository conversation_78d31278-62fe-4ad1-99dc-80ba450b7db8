import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";

// Initialize Gemini AI client with API key
function getGeminiClient() {
  const googleApiKey = process.env.GOOGLE_AI_API_KEY;
  if (!googleApiKey) {
    throw new Error("GOOGLE_AI_API_KEY environment variable is not set");
  }
  return new GoogleGenerativeAI(googleApiKey);
}

/**
 * Fetches an image from a URL and returns it as base64 data.
 */
async function fetchImageAsBase64(url: string): Promise<{ base64: string, mimeType: string }> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  if (!contentType?.startsWith('image/')) {
    throw new Error('Invalid content type: Must be an image');
  }

  const arrayBuffer = await response.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');
  
  return { base64, mimeType: contentType };
}

/**
 * Processes either a base64 string or URL into the format needed for Gemini API
 */
async function processImage(imageInput: string): Promise<{ base64: string, mimeType: string }> {
  // Check if the input is a URL
  try {
    const url = new URL(imageInput);
    if (url.protocol === 'http:' || url.protocol === 'https:') {
      return await fetchImageAsBase64(imageInput);
    }
  } catch (e) {
    // Not a URL, assume it's base64
  }

  // Handle base64 input
  const base64Data = imageInput.replace(/^data:image\/\w+;base64,/, '');
  const mimeType = imageInput.match(/^data:([^;]+);/)?.[1] || 'image/jpeg';
  
  if (!mimeType.startsWith('image/')) {
    throw new Error('Invalid content type: Must be an image');
  }
  
  return { base64: base64Data, mimeType };
}

/**
 * Calls Gemini Vision API to generate a product description from a base64 encoded image.
 * Accepts an optional category parameter to apply category-specific rules.
 * Supports different analysis types for various use cases.
 * This function must only be used in server-side code (API routes, getServerSideProps, etc.)
 */
export async function getProductDescriptionFromImage(
  base64Image: string, 
  category?: string,
  analysisType: 'product_description' | 'quality_and_angles' = 'product_description'
): Promise<string | null> {
  try {
    if (typeof window !== "undefined") {
      throw new Error("This function can only be executed server-side");
    }
    const genAI = getGeminiClient();
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    const { base64, mimeType } = await processImage(base64Image);

    // Choose prompt based on analysis type
    let prompt: string[];
    
    if (analysisType === 'quality_and_angles') {
      prompt = [
        "You are an expert image quality analyst for e-commerce product photography. Analyze this product image and provide detailed quality assessment.",
        "",
        "ANALYSIS REQUIREMENTS:",
        "1. IMAGE QUALITY ASSESSMENT:",
        "   - clarity: 'high' | 'medium' | 'low'",
        "   - lighting: 'excellent' | 'good' | 'fair' | 'poor'",
        "   - background: 'clean' | 'neutral' | 'cluttered' | 'distracting'",
        "   - resolution: 'high' | 'medium' | 'low'",
        "   - focus: 'sharp' | 'acceptable' | 'blurry'",
        "",
        "2. PRODUCT VISIBILITY:",
        "   - angle: 'front' | 'side' | 'back' | 'three-quarter' | 'top' | 'bottom' | 'detail'",
        "   - coverage: percentage of product visible (0-100)",
        "   - brandVisible: true | false",
        "   - detailsVisible: true | false",
        "",
        "3. MISSING ELEMENTS:",
        "   - missingAngles: array of angles that would improve the listing",
        "   - suggestedShots: array of additional shots needed",
        "",
        "4. PRODUCT IDENTIFICATION:",
        "   - brand: detected brand name",
        "   - model: detected model/product name",
        "   - colorway: detected color scheme",
        "   - category: product category",
        "",
        "5. RECOMMENDATIONS:",
        "   - qualityImprovements: array of specific improvements needed",
        "   - priorityLevel: 'low' | 'medium' | 'high' | 'critical'",
        "",
        "Return a JSON object with all these fields. Be specific and actionable in recommendations.",
      ];
    } else {
      // Enhanced Rivv brand-specific product analysis prompt
      prompt = [
      "You are the AI product curator for Rivv - a modern, stylish, and premium fashion brand. Your job is to analyze product images and create compelling, on-brand product listings.",
      "",
      "🔍 1. ANALYZE THE PRODUCT:",
      "Return structured data in clean JSON format with:",
      "- brand: Brand name (e.g., Nike, Adidas, Jordan)",
      "- model: Specific model name (e.g., Air Force 1, Stan Smith)",
      "- type: Product type (sneaker, hoodie, cap, t-shirt, pants, jacket, bag, accessory)",
      "- main_color: Primary color",
      "- secondary_color: Secondary color if applicable, else null",
      "- retail_price_estimate: Affordable price in Maloti (M) - price slightly below market average to be competitive and accessible",
      "- style_keywords: Array of style terms (e.g., [\"streetwear\", \"minimalist\", \"sporty\"])",
      "",
      "🏷️ 2. CREATE A CLEAN PRODUCT TITLE:",
      "Keep it clear, premium, and attractive.",
      "Examples: \"Nike Air Force 1 - Triple White\", \"Oversized Ribbed Hoodie in Graphite\"",
      "",
      "✍️ 3. WRITE A PREMIUM, ON-BRAND DESCRIPTION:",
      "- 4-7 sentences max",
      "- Stylish, confident tone",
      "- Position as must-have - highlight comfort, material, versatility, fit",
      "- Mention limited drops, versatile wear, or Rivv's curation if applicable",
      "- Example: \"A classic that never fades. The Air Force 1 in Triple White pairs effortlessly with any outfit. Built for everyday flex, this staple sneaker belongs in every rotation.\"",
      "",
      "🔖 4. SUGGEST PRODUCT TAGS:",
      "JSON array of clean, lowercase terms (e.g., [\"air force 1\", \"white sneaker\", \"streetwear\", \"unisex\", \"clean fit\"])",
      "",
      "🗂️ 5. SUGGEST CATEGORY:",
      "Must match system structure: [\"sneakers\", \"clothing\", \"headwear\", \"intimates\", \"accessories\"]",
      "",
      "📣 6. BRAND VOICE - Match Rivv's tone:",
      "- Clean, stylish, slightly edgy",
      "- Professional but youthful",
      "- Bold but not arrogant",
      "- Always elevating the product and experience",
      "",
      "OUTPUT FORMAT:",
      "Return a JSON object with these keys:",
      "- name: Clean product title",
      "- brand: Brand name",
      "- model: Model name",
      "- type: Product type",
      "- category: Product category",
      "- main_color: Primary color",
      "- secondary_color: Secondary color or null",
      "- description: Premium 4-7 sentence description",
      "- style_keywords: Array of style terms",
      "- tags: Array of product tags",
      "- gender: (Men's, Women's, Unisex, or omit if not relevant)",
      "- sizes: (Array of sizes based on gender: Women's = sizes 3-7, Men's = sizes 5 and up, Unisex = all sizes 3-12)",
      "- stock: Random number between 15-45",
      "- price: Affordable price in Maloti (M) - aim for competitive pricing that's accessible but maintains quality perception",
      "- discountedPrice: Discounted price if relevant, else omit",
      "",
      "EXAMPLES:",
      "Unisex Sneaker: {\"name\":\"Nike Air Force 1 - Triple White\",\"brand\":\"Nike\",\"model\":\"Air Force 1\",\"type\":\"sneaker\",\"category\":\"sneakers\",\"main_color\":\"white\",\"secondary_color\":null,\"description\":\"A timeless icon that transcends trends. The Air Force 1 in crisp Triple White delivers unmatched versatility and street credibility. Premium leather construction meets legendary comfort for the ultimate everyday flex. This is the foundation every rotation needs.\",\"style_keywords\":[\"classic\",\"streetwear\",\"versatile\"],\"tags\":[\"air force 1\",\"white sneaker\",\"nike\",\"classic\",\"unisex\"],\"gender\":\"Unisex\",\"sizes\":[\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],\"stock\":28,\"price\":1200,\"retail_price_estimate\":1200}",
      "Women's Sneaker: {\"name\":\"Nike Air Max 90 - Pink\",\"brand\":\"Nike\",\"model\":\"Air Max 90\",\"type\":\"sneaker\",\"category\":\"sneakers\",\"main_color\":\"pink\",\"secondary_color\":\"white\",\"description\":\"Step into comfort and style with these stunning Air Max 90s. The perfect blend of retro charm and modern appeal, featuring visible Air cushioning and premium materials. These sneakers elevate any outfit with their feminine colorway and iconic silhouette.\",\"style_keywords\":[\"retro\",\"feminine\",\"comfortable\"],\"tags\":[\"air max 90\",\"pink sneaker\",\"nike\",\"women's\",\"retro\"],\"gender\":\"Women's\",\"sizes\":[\"3\",\"4\",\"5\",\"6\",\"7\"],\"stock\":25,\"price\":1350,\"retail_price_estimate\":1350}",
      "Men's Sneaker: {\"name\":\"Adidas Samba - Black White\",\"brand\":\"Adidas\",\"model\":\"Samba\",\"type\":\"sneaker\",\"category\":\"sneakers\",\"main_color\":\"black\",\"secondary_color\":\"white\",\"description\":\"Classic football heritage meets street style. The Samba in timeless Black and White delivers authentic retro vibes with modern comfort. Premium suede construction and the iconic three stripes make this a versatile essential for any wardrobe.\",\"style_keywords\":[\"classic\",\"retro\",\"football\"],\"tags\":[\"samba\",\"black sneaker\",\"adidas\",\"men's\",\"classic\"],\"gender\":\"Men's\",\"sizes\":[\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],\"stock\":30,\"price\":1400,\"retail_price_estimate\":1400}",
      "Clothing: {\"name\":\"Oversized Ribbed Hoodie - Graphite\",\"brand\":\"Essentials\",\"model\":\"Ribbed Hoodie\",\"type\":\"hoodie\",\"category\":\"clothing\",\"main_color\":\"grey\",\"secondary_color\":null,\"description\":\"Elevated comfort meets contemporary style. This oversized ribbed hoodie in sophisticated Graphite delivers premium softness with an effortlessly cool silhouette. Perfect for layering or solo styling - it's the piece that elevates any casual moment.\",\"style_keywords\":[\"oversized\",\"minimalist\",\"comfort\"],\"tags\":[\"hoodie\",\"oversized\",\"grey\",\"ribbed\",\"unisex\"],\"gender\":\"Unisex\",\"sizes\":[\"S\",\"M\",\"L\",\"XL\"],\"stock\":22,\"price\":750,\"retail_price_estimate\":750}",
      "",
      "CRITICAL GUIDELINES:",
      "- Use official brand names and model names",
      "- Never mention flaws, condition, or negative aspects",
      "- Always maintain Rivv's premium, youthful brand voice",
      "- PRICING STRATEGY: Price products to be affordable and competitive - aim for 15-25% below typical market prices while maintaining quality perception",
      "- SIZING RULES: Women's shoes = sizes 3-7 only, Men's shoes = sizes 5-12, Unisex = all sizes 3-12",
      "- For clothing: Use standard sizing (XS, S, M, L, XL, XXL) regardless of gender",
      "- Return valid JSON only"
      ];
    }
    
    const promptText = prompt.join("\n");

    const result = await model.generateContent([
      { text: promptText },
      {
        inlineData: {
          data: base64,
          mimeType
        }
      }
    ]);
    const response = await result.response;
    const text = response.text();
    if (!text) {
      throw new Error("Gemini API returned empty response");
    }
    // Try to extract JSON from the response
    let jsonStart = text.indexOf('{');
    let jsonEnd = text.lastIndexOf('}');
    let jsonString = (jsonStart !== -1 && jsonEnd !== -1) ? text.substring(jsonStart, jsonEnd + 1) : text;
    try {
      JSON.parse(jsonString);
    } catch {
      // fallback: try to fix common issues
      jsonString = text.replace(/^[^\{]*/, '').replace(/[^\}]*$/, '');
    }
    return jsonString;
  } catch (error) {
    console.error("Gemini API error:", error);
    return null;
  }
}

/**
 * Analyze image quality and angles specifically
 */
export async function analyzeImageQuality(imageInput: string): Promise<any> {
  return getProductDescriptionFromImage(imageInput, undefined, 'quality_and_angles');
}

/**
 * Batch analyze multiple images
 */
export async function batchAnalyzeImages(
  images: Array<{ url: string; productId: string; category?: string }>,
  analysisType: 'product_description' | 'quality_and_angles' = 'product_description'
): Promise<Array<{ productId: string; result: any; error?: string }>> {
  const results = [];
  
  for (const image of images) {
    try {
      const result = await getProductDescriptionFromImage(image.url, image.category, analysisType);
      results.push({ productId: image.productId, result });
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      results.push({ 
        productId: image.productId, 
        result: null, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
  
  return results;
}
