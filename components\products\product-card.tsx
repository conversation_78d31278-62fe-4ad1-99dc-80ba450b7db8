"use client";

import { Product } from "@/utils/types";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, ShoppingCart, Star, Eye, ZoomIn, Scale } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { calculateDiscountPercentage, formatPrice, isProductOnSale, getEffectivePrice } from "@/lib/product-utils";
import { useCart } from "@/contexts/cart-context";
import { useComparison } from "@/contexts/comparison-context";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

interface ProductCardProps {
  product: Product;
  onQuickView?: (product: Product) => void;
}

export default function ProductCard({ product, onQuickView }: ProductCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const { addItem } = useCart();
  const { addToComparison, removeFromComparison, isInComparison } = useComparison();

  const isOnSale = isProductOnSale(product);
  const effectivePrice = getEffectivePrice(product);
  const discountPercentage = isOnSale 
    ? calculateDiscountPercentage(product.price, product.discountedPrice!)
    : 0;

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (product.stock <= 0 || isAddingToCart) return;

    setIsAddingToCart(true);

    try {
      // For quick add from card, add with default options (no size selection)
      // Only add if product doesn't require size selection
      if (product.sizes.length === 0) {
        addItem(product, 1);
      } else {
        // If product requires size selection, redirect to product page
        window.location.href = `/products/${product.id}`;
        return;
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorited(!isFavorited);
    // TODO: Implement favorites functionality
  };

  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onQuickView?.(product);
  };

  const handleImageHover = (index: number) => {
    if (product.images.length > 1) {
      setCurrentImageIndex(index);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-4 w-4 fill-yellow-400/50 text-yellow-400" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <motion.div
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Link href={`/products/${product.id}`}>
        <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl border-0 shadow-md text-sm sm:text-base overflow-hidden">
          <CardHeader className="p-0 relative">
            {/* Product Image with Enhanced Hover Effects */}
            <div className="relative aspect-square overflow-hidden bg-gray-100">
            {!imageError && product.images.length > 0 ? (
              <div className="relative w-full h-full">
                <AnimatePresence mode="wait">
                  <motion.img
                    key={currentImageIndex}
                    src={product.images[currentImageIndex] || product.images[0]}
                    alt={product.name}
                    className={`w-full h-full object-cover transition-all duration-500 ${
                      imageLoading ? "opacity-0" : "opacity-100"
                    }`}
                    initial={{ opacity: 0, scale: 1.1 }}
                    animate={{ opacity: 1, scale: isHovered ? 1.1 : 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    onLoad={() => setImageLoading(false)}
                    onError={() => {
                      setImageError(true);
                      setImageLoading(false);
                    }}
                  />
                </AnimatePresence>

                {/* Image Indicators for Multiple Images */}
                {product.images.length > 1 && isHovered && (
                  <motion.div
                    className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {product.images.slice(0, 4).map((_, index) => (
                      <button
                        key={index}
                        className={cn(
                          "w-2 h-2 rounded-full transition-all duration-200",
                          currentImageIndex === index
                            ? "bg-white shadow-lg"
                            : "bg-white/50 hover:bg-white/75"
                        )}
                        onMouseEnter={() => handleImageHover(index)}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setCurrentImageIndex(index);
                        }}
                      />
                    ))}
                  </motion.div>
                )}
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-200">
                <div className="text-gray-400 text-center">
                  <svg
                    className="mx-auto h-12 w-12 mb-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <span className="text-sm">No image</span>
                </div>
              </div>
            )}

            {/* Discount Badge */}
            {isOnSale && (
              <Badge
                variant="destructive"
                className="absolute top-2 left-2 z-10"
              >
                -{discountPercentage}%
              </Badge>
            )}

            {/* Stock Status Badge */}
            {product.stock <= 0 && (
              <Badge
                variant="secondary"
                className="absolute top-2 left-2 z-10 bg-gray-800 text-white"
                style={{ top: isOnSale ? '3rem' : '0.5rem' }}
              >
                Out of Stock
              </Badge>
            )}
            {product.stock > 0 && product.stock <= 5 && (
              <Badge
                variant="outline"
                className="absolute top-2 left-2 z-10 bg-orange-100 text-orange-800 border-orange-300"
                style={{ top: isOnSale ? '3rem' : '0.5rem' }}
              >
                Only {product.stock} left
              </Badge>
            )}

            {/* Favorite Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 z-10 h-8 w-8 p-0 bg-white/80 hover:bg-white"
              onClick={handleToggleFavorite}
            >
              <Heart
                className={`h-4 w-4 ${
                  isFavorited ? "fill-red-500 text-red-500" : "text-gray-600"
                }`}
              />
            </Button>

            {/* Comparison Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-12 z-10 h-8 w-8 p-0 bg-white/80 hover:bg-white"
              onClick={() => {
                if (isInComparison(product.id)) {
                  removeFromComparison(product.id);
                } else {
                  addToComparison(product);
                }
              }}
            >
              <Scale
                className={`h-4 w-4 ${
                  isInComparison(product.id) ? "fill-primary text-primary" : "text-gray-600"
                }`}
              />
            </Button>

            {/* Enhanced Action Buttons */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  className="absolute bottom-2 left-2 right-2 flex gap-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Quick View Button */}
                  {onQuickView && (
                    <Button
                      size="sm"
                      variant="secondary"
                      className="flex-1 bg-white/90 hover:bg-white text-gray-900 border-0"
                      onClick={handleQuickView}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Quick View
                    </Button>
                  )}

                  {/* Add to Cart Button */}
                  <Button
                    size="sm"
                    className={cn(
                      "text-xs sm:text-sm transition-all duration-200",
                      onQuickView ? "flex-1" : "w-full"
                    )}
                    onClick={handleAddToCart}
                    disabled={product.stock <= 0 || isAddingToCart}
                  >
                    {isAddingToCart ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Adding...
                      </>
                    ) : product.sizes.length > 0 || product.colors.length > 0 ? (
                      <>
                        <ShoppingCart className="h-4 w-4 mr-1" />
                        Select
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-4 w-4 mr-1" />
                        Add to Cart
                      </>
                    )}
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardHeader>

        <CardContent className="p-3 sm:p-4 space-y-2 sm:space-y-3">
          {/* Brand */}
          <div className="text-sm text-gray-500 font-medium">
            {product.brand}
          </div>

          {/* Product Name */}
          <h3 className="font-semibold text-gray-900 line-clamp-2 group-hover:text-primary transition-colors text-base sm:text-lg">
            {product.name}
          </h3>

          {/* Rating */}
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              {renderStars(product.rating)}
            </div>
            <span className="text-sm text-gray-500">
              ({product.reviewCount})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(effectivePrice)}
              </span>
              {isOnSale && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>
          </div>

          {/* Stock Status */}
          <div className="flex items-center justify-between">
            <div className="text-sm">
              {product.stock > 0 ? (
                <span className="text-green-600">In Stock ({product.stock})</span>
              ) : (
                <span className="text-red-600">Out of Stock</span>
              )}
            </div>
          </div>

          {/* Available Sizes (if any) */}
          {product.sizes.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {product.sizes.slice(0, 4).map((size) => (
                <Badge key={size} variant="outline" className="text-xs">
                  {size}
                </Badge>
              ))}
              {product.sizes.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{product.sizes.length - 4}
                </Badge>
              )}
            </div>
          )}

          {/* Available Colors (if any) */}
          {product.colors && product.colors.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {product.colors.slice(0, 3).map((color) => (
                <Badge key={color} variant="secondary" className="text-xs">
                  {color}
                </Badge>
              ))}
              {product.colors.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{product.colors.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
    </motion.div>
  );
}
