"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  FileImage,
  Package,
  CheckCircle,
  AlertCircle,
  Download,
} from "lucide-react";
import { User } from "@/utils/types";
import { useIsMobile } from "@/hooks/use-media-query";
import BulkImageUpload from "./bulk-image-upload";
import ProductMetadataEntry from "./product-metadata-entry";
import ProductPreview from "./product-preview";


export interface UploadedImage {
  id: string;
  url: string;
  name: string;
  size: number;
  assigned?: boolean;
}

export interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  discountedPrice?: number;
  brand: string;
  categoryId: string;
  images: string[];
  sizes: string[];

  stock: number;
  isActive: boolean;
  aiAnalysis?: {
    model?: string;
    type?: string;
    main_color?: string;
    secondary_color?: string;
    style_keywords?: string[];
    tags?: string[];
    gender?: string;
    retail_price_estimate?: number;
    generated_at?: string;
    source?: string;
  };
}

export interface BulkUploadState {
  step: "images" | "metadata" | "preview" | "complete";
  uploadedImages: UploadedImage[];
  products: ProductData[];
  isProcessing: boolean;
  progress: number;
  errors: string[];
  successCount: number;
}

type Props = {
  user: User;
};

export default function BulkProductUploadContent({ user }: Props) {
  const [state, setState] = useState<BulkUploadState>({
    step: "images",
    uploadedImages: [],
    products: [],
    isProcessing: false,
    progress: 0,
    errors: [],
    successCount: 0,
  });
  const [categories, setCategories] = useState<
    Array<{ id: string; name: string }>
  >([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loadingMetadata, setLoadingMetadata] = useState(false);
  const isMobile = useIsMobile();
  const router = useRouter();

  // Check user role on client side
  useEffect(() => {
    setUserRole(user.role);
  }, [router]);

  // Load categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error("Failed to fetch categories:", error);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Don't render content until role is verified
  if (userRole !== "ADMIN") {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying access...</p>
        </div>
      </div>
    );
  }

  const updateState = (updates: Partial<BulkUploadState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Function to automatically select category based on AI analysis
  const selectCategoryFromAI = (aiData: any): string => {
    if (!aiData || !categories.length) {
      return categories[0]?.id || "";
    }

    const productType = aiData.type?.toLowerCase() || "";
    const productName = aiData.name?.toLowerCase() || "";
    const description = aiData.description?.toLowerCase() || "";

    // Create a combined text for analysis
    const combinedText = `${productType} ${productName} ${description}`;

    // Category mapping logic
    const categoryMappings = [
      {
        keywords: ['sneaker', 'shoe', 'boot', 'sandal', 'slipper', 'loafer', 'trainer', 'runner', 'basketball', 'tennis', 'athletic', 'jordan', 'nike', 'adidas', 'converse', 'vans'],
        categoryNames: ['sneakers', 'sneaker']
      },
      {
        keywords: ['cap', 'hat', 'beanie', 'headband', 'headwear', 'bucket hat', 'snapback', 'baseball cap'],
        categoryNames: ['headwear', 'headware']
      },
      {
        keywords: ['underwear', 'boxer', 'brief', 'panty', 'bra', 'lingerie', 'intimate', 'undergarment'],
        categoryNames: ['intimates', 'intimate']
      },
      {
        keywords: ['shirt', 'hoodie', 'jacket', 'dress', 'pants', 'jeans', 'sweater', 'top', 'bottom', 'clothing', 'apparel', 'fashion'],
        categoryNames: ['fashionwear', 'fashion', 'clothing', 'apparel']
      }
    ];

    // Find matching category
    for (const mapping of categoryMappings) {
      const hasKeyword = mapping.keywords.some(keyword => combinedText.includes(keyword));
      if (hasKeyword) {
        // Find the category by name
        const matchingCategory = categories.find(cat =>
          mapping.categoryNames.some(name => cat.name.toLowerCase().includes(name))
        );
        if (matchingCategory) {
          return matchingCategory.id;
        }
      }
    }

    // Default to first category if no match found
    return categories[0]?.id || "";
  };

  const handleImagesUploaded = async (images: UploadedImage[]) => {
    // Clean up any previous state
    localStorage.removeItem("bulk-upload-products");
    setState((prev) => ({
      ...prev,
      uploadedImages: images,
      products: [], // Reset products to avoid reusing old metadata
      errors: [],
    }));
    if (images.length === 0) {
      setState((prev) => ({ ...prev, step: "images" }));
      return;
    }
    setLoadingMetadata(true);
    try {
      const response = await fetch("/api/admin/products/bulk/metadata", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ images: images.map((img) => img.url) }),
      });
      const result = await response.json();
      if (result.success && result.data && Array.isArray(result.data.results)) {
        // Separate successful and failed results
        const successful = result.data.results.filter((d: any) => d.success && d.data);
        const failed = result.data.results.filter((d: any) => !d.success || !d.data);
        // Filter out any null or undefined images and map to products
        const products = await Promise.all(images
          .filter(img => img && img.url)
          .map(async (img): Promise<ProductData & { images_by_angle?: any, images_source?: string }> => {
            const ai = successful.find((d: any) => d.imageUrl === img.url);
            let name = "Unnamed Product";
            let brand = "Unknown Brand";
            let description = "";
            let price = 0;
            let discountedPrice: number | undefined = undefined;
            let colorway = "";
            if (ai?.data) {
              try {
                const parsed = ai.data;
                name = parsed.name || "Unnamed Product";
                brand = parsed.brand || "Unknown Brand";
                description = parsed.description || "";
                price = typeof parsed.price === 'number' ? parsed.price : 0;
                discountedPrice = typeof parsed.discountedPrice === 'number' ? parsed.discountedPrice : undefined;
                colorway = parsed.colorway || '';
              } catch (e) {
                console.error('Failed to parse AI response:', e);
              }
            }
            // Use original image without enhancement
            const images_by_angle = [{ angle: 'main', url: img.url }];
            const images_source = 'original';
            // Prepare the product data
            const productData = {
              id: img.id,
              name: name || "Unnamed Product",
              description,
              price,
              discountedPrice,
              brand,
              categoryId: selectCategoryFromAI(ai?.data) || categories[0]?.id || "",
              images: images_by_angle.map((i: { angle: string; url: string }) => i.url),
              sizes: ai?.data?.sizes || [],
              stock: ai?.data?.stock || 0,
              isActive: true,
              images_by_angle,
              images_source
            };

            // Add AI analysis data if available
            if (ai?.data) {
              const parsed = ai.data;
              (productData as any).aiAnalysis = {
                model: parsed.model,
                type: parsed.type,
                main_color: parsed.main_color,
                secondary_color: parsed.secondary_color,
                style_keywords: parsed.style_keywords || [],
                tags: parsed.tags || [],
                gender: parsed.gender,
                retail_price_estimate: parsed.retail_price_estimate,
                generated_at: new Date().toISOString(),
                source: 'gemini_bulk_upload'
              };

              // Set colors array from AI data
              (productData as any).colors = [parsed.main_color, parsed.secondary_color].filter(Boolean);
            }

            return productData;
          }));
        let errors: string[] = [];
        if (failed.length > 0) {
          errors = failed.map((f: any) => {
            if (f.error) {
              // Gemini-specific error handling
              if (f.error.includes("API key not valid")) {
                return `Image: ${f.imageUrl} - Gemini API key is invalid. Please check your API key.`;
              }
              if (f.error.includes("quota")) {
                return `Image: ${f.imageUrl} - Gemini API quota exceeded. Please wait or upgrade your quota.`;
              }
              if (f.error.includes("image not accessible")) {
                return `Image: ${f.imageUrl} - Image is not accessible to Gemini. Make sure the image URL is public and not expired.`;
              }
              if (f.error.includes("Failed to parse AI response")) {
                return `Image: ${f.imageUrl} - AI response could not be parsed. The image may not contain a clear product.`;
              }
              if (f.error.includes("No description generated")) {
                return `Image: ${f.imageUrl} - AI could not generate a description. The image may not be clear or may not contain a recognizable product.`;
              }
              return `Image: ${f.imageUrl} - Error: ${f.error}`;
            }
            return `Image: ${f.imageUrl} - No description returned. This may happen if the image is not clear, not a product, or Gemini could not generate a description.`;
          });
        }
        setState((prev) => ({ ...prev, products, step: "metadata", errors }));
      } else {
        // Check if this is an authentication error
        if (result.details === 'NEXT_REDIRECT') {
          setState((prev) => ({ ...prev, step: "metadata", errors: [
            "Authentication error. Please refresh the page and make sure you're logged in as an admin."
          ] }));
        } else {
          // Check for specific error types
          let errorMessage = "Failed to extract product details from images.";
          if (result.error?.includes('API key')) {
            errorMessage = "Gemini API key issue. Please check your API key configuration.";
          } else if (result.error?.includes('quota')) {
            errorMessage = "Gemini API quota exceeded. Please wait or upgrade your quota.";
          } else if (result.details) {
            errorMessage = `Error: ${result.details}`;
          }

          setState((prev) => ({ ...prev, step: "metadata", errors: [errorMessage] }));
        }
      }
    } catch (error: any) {
      let message = "Error extracting product details from images.";
      if (error?.message?.includes("API key not valid")) {
        message = "Gemini API key is invalid. Please check your API key.";
      } else if (error?.message?.includes("quota")) {
        message = "Gemini API quota exceeded. Please wait or upgrade your quota.";
      }
      setState((prev) => ({ ...prev, step: "metadata", errors: [message] }));
    } finally {
      setLoadingMetadata(false);
    }
  };

  const handleProductsCreated = (products: ProductData[]) => {
    // Clean up any stored images and product data
    localStorage.removeItem("bulk-upload-products");
    updateState({
      products,
      step: products.length > 0 ? "preview" : "metadata",
    });
  };

  const handleSubmitProducts = async () => {
    updateState({ isProcessing: true, progress: 0, errors: [] });

    try {
      const response = await fetch("/api/admin/products/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ products: state.products }),
      });

      const result = await response.json();

      if (result.success) {
        updateState({
          step: "complete",
          successCount: result.data.summary.successful,
          errors: result.data.errors,
          progress: 100,
        });
      } else {
        updateState({
          errors: result.details || [result.error],
          progress: 0,
        });
      }
    } catch (error) {
      updateState({
        errors: ["Failed to submit products. Please try again."],
        progress: 0,
      });
    } finally {
      updateState({ isProcessing: false });
    }
  };

  const downloadTemplate = async () => {
    try {
      const response = await fetch("/api/admin/products/bulk/template");
      const result = await response.json();

      if (result.success) {
        const blob = new Blob([JSON.stringify(result.data, null, 2)], {
          type: "application/json",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "bulk-upload-template.json";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Failed to download template:", error);
    }
  };

  const getStepStatus = (step: string) => {
    const currentStepIndex = [
      "images",
      "metadata",
      "preview",
      "complete",
    ].indexOf(state.step);
    const stepIndex = ["images", "metadata", "preview", "complete"].indexOf(
      step
    );

    if (stepIndex < currentStepIndex) return "complete";
    if (stepIndex === currentStepIndex) return "current";
    return "pending";
  };

  const renderStepIndicator = () => (
    <div className="w-full overflow-x-auto whitespace-nowrap -mx-4 px-4 scrollbar-hide mb-4">
      <div className="flex items-center justify-center min-w-max space-x-4">
        {[
          { key: "images", label: "Upload Images", icon: FileImage },
          { key: "metadata", label: "Product Details", icon: Package },
          { key: "preview", label: "Review & Submit", icon: CheckCircle },
        ].map(({ key, label, icon: Icon }, index) => {
          const status = getStepStatus(key);
          return (
            <div key={key} className="flex items-center">
              <div
                className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2
              ${
                status === "complete"
                  ? "bg-green-500 border-green-500 text-white"
                  : status === "current"
                  ? "bg-blue-500 border-blue-500 text-white"
                  : "bg-gray-200 border-gray-300 text-gray-500"
              }
            `}
              >
                <Icon className="w-5 h-5" />
              </div>
              <span
                className={`ml-2 text-sm font-medium ${
                  status === "current"
                    ? "text-blue-600"
                    : status === "complete"
                    ? "text-green-600"
                    : "text-gray-500"
                }`}
              >
                {label}
              </span>
              {index < 2 && (
                <div
                  className={`w-8 h-0.5 mx-4 ${
                    status === "complete" ? "bg-green-500" : "bg-gray-300"
                  }`}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-6">
        <div className="flex flex-col xs:flex-row xs:items-center gap-2 xs:gap-4">
          <Button
            variant="outline"
            onClick={() => router.push("/admin/products")}
            className="flex items-center space-x-2 w-full xs:w-auto"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Products</span>
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 leading-tight">
              Bulk Product Upload
            </h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">
              Upload multiple products efficiently
            </p>
          </div>
        </div>
        <div className="flex flex-col xs:flex-row gap-2 mt-2 sm:mt-0">
          <Button variant="outline" onClick={downloadTemplate} className="w-full xs:w-auto">
            <Download className="w-4 h-4 mr-2" />
            Download Template
          </Button>
          <Badge variant="outline" className="text-sm w-full xs:w-auto">
            {state.uploadedImages.length} images • {state.products.length} products
          </Badge>
        </div>
      </div>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Progress Bar */}
      {state.isProcessing && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Processing products...
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(state.progress)}%
            </span>
          </div>
          <Progress value={state.progress} className="w-full" />
        </div>
      )}

      {/* Error Display */}
      {state.errors.length > 0 && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {state.errors.slice(0, 5).map((error, index) => (
                <div key={index}>{error}</div>
              ))}
              {state.errors.length > 5 && (
                <div className="text-sm text-gray-600">
                  ... and {state.errors.length - 5} more errors
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Card>
        <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
          {state.step === "images" && (
            <BulkImageUpload
              onImagesUploaded={handleImagesUploaded}
              uploadedImages={state.uploadedImages}
            />
          )}

          {loadingMetadata && (
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              <Progress value={60} className={`${isMobile ? 'w-3/4' : 'w-1/2'}`} />
              <span className={`text-gray-600 text-center ${isMobile ? 'text-sm' : ''}`}>
                Analyzing images with AI...
              </span>
            </div>
          )}

          {state.step === "metadata" && !loadingMetadata && (
            <ProductMetadataEntry
              uploadedImages={state.uploadedImages}
              categories={categories}
              loadingCategories={loadingCategories}
              onProductsCreated={handleProductsCreated}
              onBack={() => setState((prev) => ({ ...prev, step: "images" }))}
              initialProducts={state.products}
            />
          )}

          {state.step === "preview" && (
            <ProductPreview
              products={state.products}
              uploadedImages={state.uploadedImages}
              onSubmit={handleSubmitProducts}
              onBack={() => setState((prev) => ({ ...prev, step: "metadata" }))}
              isProcessing={state.isProcessing}
            />
          )}

          {state.step === "complete" && (
            <div className="text-center py-12">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Upload Complete!
              </h2>
              <p className="text-gray-600 mb-6">
                Successfully created {state.successCount} products
                {state.errors.length > 0 &&
                  ` with ${state.errors.length} errors`}
              </p>
              <div className="flex justify-center space-x-4">
                <Button onClick={() => router.push("/admin/products")}>
                  View Products
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    setState({
                      step: "images",
                      uploadedImages: [],
                      products: [],
                      isProcessing: false,
                      progress: 0,
                      errors: [],
                      successCount: 0,
                    })
                  }
                >
                  Upload More
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
